/* Modern Home Page Design for Corporate Prompt Master
 * A sleek, professional design with modern aesthetics and improved visual hierarchy
 */

/* Base styles for the hero section */
.hero-section {
  position: relative;
  padding: 5rem 0;
  overflow: hidden;
  border-radius: 0;
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: none;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
}

/* Remove the blue padding with black inner contents */
.hero-section.jumbotron {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 0;
  margin-bottom: 0;
  padding: 5rem 0;
  border: none;
}

/* Full-width hero section */
.hero-section.jumbotron {
  width: 100%;
  max-width: 100%;
  border-radius: 0;
}

/* Hero content container */
.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero title styling */
.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Hero subtitle styling */
.hero-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
}

/* Hero buttons container */
.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Animated background elements */
.hero-bg-element {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.3), rgba(139, 92, 246, 0.3));
  filter: blur(60px);
  z-index: 1;
}

.hero-bg-element-1 {
  width: 400px;
  height: 400px;
  top: -100px;
  right: -100px;
  animation: float 15s ease-in-out infinite;
}

.hero-bg-element-2 {
  width: 300px;
  height: 300px;
  bottom: -50px;
  left: -50px;
  animation: float 20s ease-in-out infinite reverse;
}

@keyframes float {
  0% { transform: translate(0, 0) rotate(0deg); }
  50% { transform: translate(30px, 30px) rotate(5deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}

/* Main content container */
.main-content {
  background-color: #121212;
  padding: 4rem 0;
}

/* Feature cards */
.feature-card {
  height: 100%;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: linear-gradient(145deg, #1e1e1e 0%, #2a2a2a 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-card:hover {
  transform: translateY(-10px);
  border-color: rgba(58, 134, 255, 0.5);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.feature-card .card-body {
  padding: 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.15), rgba(139, 92, 246, 0.15));
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(58, 134, 255, 0.3);
}

.feature-card:hover .feature-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.3), rgba(139, 92, 246, 0.3));
}

.feature-icon {
  font-size: 2.5rem;
  color: #3a86ff;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  color: #0ea5e9;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
}

.feature-description {
  color: #e0e0e0;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

/* Info section */
.info-section {
  padding: 4rem 0;
  position: relative;
}

.info-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  color: #ffffff;
}

.info-section .lead {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
  color: #e0e0e0;
}

/* CTA section */
.cta-section {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  padding: 4rem 0;
  text-align: center;
  border-radius: 12px;
  margin: 4rem 0;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.cta-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.cta-section p {
  font-size: 1.25rem;
  max-width: 700px;
  margin: 0 auto 2rem;
  color: #e0e0e0;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .hero-section {
    padding: 3rem 0;
  }
  
  .feature-card .card-body {
    padding: 1.5rem;
  }
}
